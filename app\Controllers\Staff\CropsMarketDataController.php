<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\CropsFarmMarketingDataModel;
use App\Models\CropsModel;

class CropsMarketDataController extends BaseController
{
    protected $cropsFarmMarketingDataModel;
    protected $cropsModel;

    public function __construct()
    {
        $this->cropsFarmMarketingDataModel = new CropsFarmMarketingDataModel();
        $this->cropsModel = new CropsModel();
    }

    // Index method to display all crops market data
    public function index()
    {
        $data['marketingData'] = $this->cropsFarmMarketingDataModel->getMarketingReportData();
        return view('staff_crops_market/staff_crops_market_data_index', $data);
    }

    // Create method to display the form for adding new crops market data
    public function create()
    {
        $data['crops'] = $this->cropsModel->getActiveCrops();
        return view('staff_crops_market/staff_crops_market_data_create', $data);
    }

    // Store method to handle form submission for adding new crops market data
    public function store()
    {
        $postData = $this->request->getPost();
        $insertId = $this->cropsFarmMarketingDataModel->insert($postData);

        if ($insertId) {
            session()->setFlashdata('success', 'Crops market data added successfully.');
        } else {
            session()->setFlashdata('error', 'Failed to add crops market data.');
        }

        return redirect()->to(base_url('staff/farms/marketing_data'));
    }

    // Show method to display a single crops market data record
    public function show($id)
    {
        $data['marketingData'] = $this->cropsFarmMarketingDataModel->find($id);
        return view('staff_crops_market/staff_crops_market_data_show', $data);
    }

    // Edit method to display the form for editing a crops market data record
    public function edit($id)
    {
        $data['marketingData'] = $this->cropsFarmMarketingDataModel->find($id);
        $data['crops'] = $this->cropsModel->getActiveCrops();
        return view('staff_crops_market/staff_crops_market_data_edit', $data);
    }

    // Update method to handle form submission for updating a crops market data record
    public function update($id)
    {
        $postData = $this->request->getPost();
        $updateResult = $this->cropsFarmMarketingDataModel->update($id, $postData);

        if ($updateResult) {
            session()->setFlashdata('success', 'Crops market data updated successfully.');
        } else {
            session()->setFlashdata('error', 'Failed to update crops market data.');
        }

        return redirect()->to(base_url('staff/farms/marketing_data'));
    }

    // Delete method to display the confirmation form for deleting a crops market data record
    public function delete($id)
    {
        $data['marketingData'] = $this->cropsFarmMarketingDataModel->find($id);
        return view('staff_crops_market/staff_crops_market_data_delete', $data);
    }

    // Destroy method to handle the deletion of a crops market data record
    public function destroy($id)
    {
        $deleteResult = $this->cropsFarmMarketingDataModel->delete($id);

        if ($deleteResult) {
            session()->setFlashdata('success', 'Crops market data deleted successfully.');
        } else {
            session()->setFlashdata('error', 'Failed to delete crops market data.');
        }

        return redirect()->to(base_url('staff/farms/marketing_data'));
    }
}
