<?php

namespace App\Controllers\Staff;

use App\Models\FieldVisitsModel;
use App\Models\countryModel;
use App\Models\provinceModel;
use App\Models\districtModel;
use App\Models\llgModel;
use App\Models\UsersModel;
use CodeIgniter\RESTful\ResourceController;

class StaffFieldVisitController extends ResourceController
{
    protected $fieldVisitsModel;
    protected $countryModel;
    protected $provinceModel;
    protected $districtModel;
    protected $llgModel;
    protected $usersModel;
    protected $helpers = ['form', 'url', 'file', 'text', 'info'];

    public function __construct()
    {
        $this->fieldVisitsModel = new FieldVisitsModel();
        $this->countryModel = new countryModel();
        $this->provinceModel = new provinceModel();
        $this->districtModel = new districtModel();
        $this->llgModel = new llgModel();
        $this->usersModel = new UsersModel();
    }

    /**
     * Display a list of all field visits
     */
    public function index()
    {
        // Get all field visits
        $visits = $this->fieldVisitsModel
            ->select('field_visits.*, c.name as country_name, p.name as province_name, d.name as district_name, l.name as llg_name')
            ->join('adx_country c', 'c.id = field_visits.country_id', 'left')
            ->join('adx_province p', 'p.id = field_visits.province_id', 'left')
            ->join('adx_district d', 'd.id = field_visits.district_id', 'left')
            ->join('adx_llg l', 'l.id = field_visits.llg_id', 'left')
            ->where('field_visits.deleted_at IS NULL')
            ->orderBy('field_visits.created_at', 'DESC')
            ->findAll();

        $data = [
            'title' => 'Field Visits',
            'page_header' => 'Field Visits Management',
            'visits' => $visits
        ];

        return view('staff_field_visit/staff_field_visit_index', $data);
    }

    /**
     * Display the form to create a new field visit
     */
    public function create()
    {
        // Get the current user's district from session
        $districtId = session()->get('district_id');

        // Get LLGs for the current district
        $llgs = $this->llgModel->where('district_id', $districtId)->findAll();

        $data = [
            'title' => 'Create Field Visit',
            'page_header' => 'Create New Field Visit',
            'llgs' => $llgs,
            'officers' => $this->usersModel->where('status', 1)->findAll()
        ];

        return view('staff_field_visit/staff_field_visit_create', $data);
    }

    /**
     * Store a newly created field visit
     */
    public function store()
    {
        // Validation rules
        $rules = [
            'llg_id' => 'required|numeric',
            'date_start' => 'required',
            'date_end' => 'required',
            'purpose' => 'required|min_length[5]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Get location data from session
        $countryId = session()->get('orgcountry_id');
        $provinceId = session()->get('orgprovince_id');
        $districtId = session()->get('district_id');

        // Process locations (JSON array)
        $locationsInput = $this->request->getPost('locations');
        $locations = !empty($locationsInput) ? json_encode(array_filter(explode("\n", $locationsInput))) : null;

        // Process GPS coordinates
        $gpsInput = $this->request->getPost('gps');
        $gps = !empty($gpsInput) ? trim($gpsInput) : null;

        // Process officers (JSON array)
        $officersInput = $this->request->getPost('officers');
        $officersData = [];
        if (!empty($officersInput)) {
            foreach ($officersInput as $officerId) {
                $officer = $this->usersModel->find($officerId);
                if ($officer) {
                    $officersData[] = [
                        'id' => $officer['id'],
                        'name' => $officer['name']
                    ];
                }
            }
        }

        // Process achievements and beneficiaries (JSON)
        $achievementsInput = $this->request->getPost('achievements');
        $achievements = !empty($achievementsInput) ? json_encode(['description' => $achievementsInput]) : null;

        $beneficiariesInput = $this->request->getPost('beneficiaries');
        $beneficiaries = !empty($beneficiariesInput) ? json_encode(['description' => $beneficiariesInput]) : null;

        // Prepare data for insertion
        $data = [
            'country_id' => $countryId,
            'province_id' => $provinceId,
            'district_id' => $districtId,
            'llg_id' => $this->request->getPost('llg_id'),
            'locations' => $locations,
            'gps' => $gps,
            'officers' => json_encode($officersData),
            'date_start' => $this->request->getPost('date_start'),
            'date_end' => $this->request->getPost('date_end'),
            'purpose' => $this->request->getPost('purpose'),
            'achievements' => $achievements,
            'beneficiaries' => $beneficiaries,
            'status' => 1, // Default active status
            'created_by' => session()->get('emp_id') ?? 1
        ];

        if ($this->fieldVisitsModel->insert($data)) {
            return redirect()->to(base_url('staff/extension/field-visits'))
                ->with('success', 'Field visit created successfully');
        } else {
            return redirect()->back()->withInput()
                ->with('error', 'Failed to create field visit');
        }
    }

    /**
     * Display a specific field visit
     */
    public function show($id = null)
    {
        $visit = $this->fieldVisitsModel
            ->select('field_visits.*, c.name as country_name, p.name as province_name, d.name as district_name, l.name as llg_name')
            ->join('adx_country c', 'c.id = field_visits.country_id', 'left')
            ->join('adx_province p', 'p.id = field_visits.province_id', 'left')
            ->join('adx_district d', 'd.id = field_visits.district_id', 'left')
            ->join('adx_llg l', 'l.id = field_visits.llg_id', 'left')
            ->find($id);

        if (!$visit) {
            return redirect()->to(base_url('staff/extension/field-visits'))
                ->with('error', 'Field visit not found');
        }

        $data = [
            'title' => 'View Field Visit',
            'page_header' => 'Field Visit Details',
            'visit' => $visit
        ];

        return view('staff_field_visit/staff_field_visit_view', $data);
    }

    /**
     * Display the form to edit a field visit
     */
    public function edit($id = null)
    {
        $visit = $this->fieldVisitsModel->find($id);

        if (!$visit) {
            return redirect()->to(base_url('staff/extension/field-visits'))
                ->with('error', 'Field visit not found');
        }

        // Get the current user's district from session
        $districtId = session()->get('district_id');

        // Get LLGs for the current district
        $llgs = $this->llgModel->where('district_id', $districtId)->findAll();

        $data = [
            'title' => 'Edit Field Visit',
            'page_header' => 'Edit Field Visit',
            'visit' => $visit,
            'llgs' => $llgs,
            'officers' => $this->usersModel->where('status', 1)->findAll()
        ];

        return view('staff_field_visit/staff_field_visit_edit', $data);
    }

    /**
     * Update a field visit
     */
    public function update($id = null)
    {
        $visit = $this->fieldVisitsModel->find($id);

        if (!$visit) {
            return redirect()->to(base_url('staff/extension/field-visits'))
                ->with('error', 'Field visit not found');
        }

        // Validation rules
        $rules = [
            'llg_id' => 'required|numeric',
            'date_start' => 'required',
            'date_end' => 'required',
            'purpose' => 'required|min_length[5]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Get location data from session
        $countryId = session()->get('orgcountry_id');
        $provinceId = session()->get('orgprovince_id');
        $districtId = session()->get('district_id');

        // Process locations (JSON array)
        $locationsInput = $this->request->getPost('locations');
        $locations = !empty($locationsInput) ? json_encode(array_filter(explode("\n", $locationsInput))) : null;

        // Process GPS coordinates
        $gpsInput = $this->request->getPost('gps');
        $gps = !empty($gpsInput) ? trim($gpsInput) : null;

        // Process officers (JSON array)
        $officersInput = $this->request->getPost('officers');
        $officersData = [];
        if (!empty($officersInput)) {
            foreach ($officersInput as $officerId) {
                $officer = $this->usersModel->find($officerId);
                if ($officer) {
                    $officersData[] = [
                        'id' => $officer['id'],
                        'name' => $officer['name']
                    ];
                }
            }
        }

        // Process achievements and beneficiaries (JSON)
        $achievementsInput = $this->request->getPost('achievements');
        $achievements = !empty($achievementsInput) ? json_encode(['description' => $achievementsInput]) : null;

        $beneficiariesInput = $this->request->getPost('beneficiaries');
        $beneficiaries = !empty($beneficiariesInput) ? json_encode(['description' => $beneficiariesInput]) : null;

        // Prepare data for update
        $data = [
            'country_id' => $countryId,
            'province_id' => $provinceId,
            'district_id' => $districtId,
            'llg_id' => $this->request->getPost('llg_id'),
            'locations' => $locations,
            'gps' => $gps,
            'officers' => json_encode($officersData),
            'date_start' => $this->request->getPost('date_start'),
            'date_end' => $this->request->getPost('date_end'),
            'purpose' => $this->request->getPost('purpose'),
            'achievements' => $achievements,
            'beneficiaries' => $beneficiaries,
            'updated_by' => session()->get('emp_id') ?? 1
        ];

        if ($this->fieldVisitsModel->update($id, $data)) {
            return redirect()->to(base_url('staff/extension/field-visits'))
                ->with('success', 'Field visit updated successfully');
        } else {
            return redirect()->back()->withInput()
                ->with('error', 'Failed to update field visit');
        }
    }

    /**
     * Delete a field visit (soft delete)
     */
    public function destroy($id = null)
    {
        $visit = $this->fieldVisitsModel->find($id);

        if (!$visit) {
            return redirect()->to(base_url('staff/extension/field-visits'))
                ->with('error', 'Field visit not found');
        }

        if ($this->fieldVisitsModel->delete($id)) {
            return redirect()->to(base_url('staff/extension/field-visits'))
                ->with('success', 'Field visit deleted successfully');
        } else {
            return redirect()->to(base_url('staff/extension/field-visits'))
                ->with('error', 'Failed to delete field visit');
        }
    }
}
